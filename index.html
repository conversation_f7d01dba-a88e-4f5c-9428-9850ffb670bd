<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <title>Shans System</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 0; /* Changed to 0 to accommodate loading screen */
      background-color: #f4f4f4;
    }
    .container {
      max-width: 800px;
      margin: auto;
      background: white;
      padding: 20px;
      box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    h1, h2 {
      color: #333;
    }
    .section {
      margin-bottom: 20px;
      border: 1px solid #ddd;
      padding: 15px;
      border-radius: 5px;
    }
    label {
      display: block;
      margin-bottom: 5px;
    }
    input, select, textarea {
      width: 100%;
      padding: 8px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    textarea {
      resize: vertical;
      min-height: 80px;
    }
    input[readonly] {
      background-color: #e9e9e9;
      cursor: not-allowed;
    }
    button {
      background-color: #4a90e2;
      color: white;
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-right: 10px;
    }
    button:hover {
      background-color: #81b0e6;
    }
    .button-group {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      table-layout: auto;
    }
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
      word-wrap: break-word;
    }
    th {
      background-color: #f2f2f2;
    }
    #productList {
      list-style-type: none;
      padding: 0;
      margin-top: 10px;
      max-height: 150px;
      overflow-y: auto;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    #productList li {
      padding: 8px;
      cursor: pointer;
      border-bottom: 1px solid #eee;
      display: flex;
      flex-direction: column;
    }
    #productList li:hover {
      background-color: #f0f0f0;
    }
    #productList li .product-name {
      font-weight: bold;
    }
    #productList li .product-details {
      font-size: 0.9em;
      color: #666;
      margin-top: 3px;
    }
    .loader {
      border: 4px solid #f3f3f3; /* Light grey */
      border-top: 4px solid #3498db; /* Blue */
      border-radius: 50%;
      width: 20px;
      height: 20px;
      animation: spin 1s linear infinite;
      display: none;
      margin-left: 10px;
      vertical-align: middle;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    .table-responsive {
      width: 100%;
      overflow-x: auto;
    }

    /* Div-based Table Styling */
    .table-container {
      width: 100%;
      margin-bottom: 20px;
      border-radius: 4px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
      overflow-x: auto; /* Allow horizontal scrolling on desktop */
    }

    .div-table {
      display: flex;
      flex-direction: column;
      width: 100%;
      background-color: white;
      font-size: 14px;
      border: 1px solid #e0e0e0;
      margin-bottom: 0;
    }

    .div-table-row {
      display: flex;
      flex-direction: row;
      border-bottom: 1px solid #e0e0e0;
    }

    .div-table-row:nth-child(even) {
      background-color: #f9f9f9;
    }

    .div-table-row:hover {
      background-color: #f0f7ff;
    }

    .div-table-header {
      background-color: #f2f2f2;
      font-weight: bold;
      border-bottom: 2px solid #d0d0d0;
      position: sticky;
      top: 0;
      z-index: 10;
    }

    .div-table-cell {
      padding: 8px 10px;
      border-right: 1px solid #e0e0e0;
      white-space: nowrap; /* No wrapping on desktop */
      overflow: hidden;
      text-overflow: ellipsis;
      display: flex;
      align-items: center;
    }

    .div-table-cell:last-child {
      border-right: none;
    }

    .div-table-heading {
      color: #333;
      font-weight: 700; /* Consistent bold weight */
    }

    /* Column-specific styling */
    .product-column {
      flex: 4;
      min-width: 250px;
      max-width: 350px;
      justify-content: flex-start;
      padding-left: 12px;
    }

    .qty-column {
      flex: 0.3;
      min-width: 30px;
      max-width: 40px;
      justify-content: center;
      font-weight: 700; /* Make it bold */
      text-align: center;
      font-size: 13px;
    }

    .price-column {
      flex: 0.7;
      min-width: 70px;
      justify-content: flex-end;
      padding-right: 12px;
      font-size: 13px;
    }

    /* For the tax column that may be hidden */
    .tax-column {
      display: flex; /* Will be toggled with JavaScript */
    }

    /* Div Table Body */
    .div-table-body {
      display: flex;
      flex-direction: column;
    }

    .new-product-label {
      color: #ff9900;
      font-weight: bold;
      margin-left: 5px;
      font-size: 12px;
    }

    /* Stock indicator styles */
    .stock-available {
      color: #28a745;
      font-weight: bold;
    }

    .stock-empty {
      color: #dc3545;
      font-weight: bold;
    }

    /* Remove X styling */
    .remove-x {
      color: #ff3b30;
      font-weight: bold;
      font-size: 16px;
      cursor: pointer;
      padding: 5px 10px;
      transition: transform 0.2s ease, color 0.2s ease;
      display: inline-block;
    }

    .remove-x:hover {
      transform: scale(1.2);
      color: #cc0000;
    }

    /* Responsive Design */
    @media (max-width: 830px) {
      body {
        padding: 10px;
      }

      .container {
        padding: 15px;
      }

      /* Adjust table for medium screens */
      .div-table-cell {
        padding: 6px 8px;
        font-size: 13px;
      }

      /* Adjust column widths for medium screens */
      .product-column {
        min-width: 180px;
        flex: 3;
      }

      .qty-column {
        min-width: 25px;
        flex: 0.3;
        font-size: 12px;
      }

      .price-column {
        min-width: 60px;
        flex: 0.6;
        font-size: 12px;
      }
    }

    /* Specific adjustments for small screens */
    @media (max-width: 650px) {
      body {
        padding: 5px;
      }

      .container {
        padding: 12px;
      }

      /* Header button styling for mobile */
      .container > div:first-child {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 15px;
      }

      .container > div:first-child > div {
        display: flex !important;
        flex-direction: column;
        gap: 10px;
        width: 100%;
      }

      /* Admin button mobile styling */
      .admin-nav-button {
        width: 100% !important;
        padding: 12px 0 !important;
        text-align: center;
        margin-right: 0 !important;
        font-size: 16px !important;
        box-sizing: border-box;
      }

      button {
        width: 100%;
        padding: 12px 0;
        font-size: 16px;
        margin-right: 0;
      }

      .loader {
        width: 25px;
        height: 25px;
      }

      .section {
        padding: 10px;
      }

      .button-group {
        flex-direction: column;
        align-items: stretch;
      }

      /* Adjust table for small screens */
      .table-container {
        overflow-x: visible; /* Disable horizontal scrolling on mobile */
        box-shadow: none;
        border-radius: 0;
      }

      .div-table {
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
      }

      .div-table-cell {
        padding: 5px 6px;
        font-size: 12px;
      }

      .product-column {
        min-width: 0; /* Remove min-width to allow full flexibility */
        flex: 2;
      }

      .qty-column {
        min-width: 0; /* Remove min-width to allow full flexibility */
        max-width: none;
        flex: 0.25;
        font-size: 11px;
      }

      .price-column {
        min-width: 0; /* Remove min-width to allow full flexibility */
        flex: 0.5;
        font-size: 11px;
      }
    }

    /* Specific adjustments for very small screens */
    @media (max-width: 480px) {
      body {
        padding: 0;
      }

      .container {
        padding: 10px;
      }

      /* Ensure header buttons remain properly styled on very small screens */
      .admin-nav-button {
        font-size: 14px !important;
        padding: 10px 0 !important;
      }

      button {
        font-size: 14px;
        padding: 10px 0;
      }

      /* Mobile-optimized table */
      .div-table-cell {
        padding: 4px 5px;
        font-size: 11px;
        white-space: normal; /* Allow text wrapping on mobile */
      }

      /* Make all table headers consistent size on mobile */
      .div-table-heading {
        font-size: 11px !important; /* Override inline styles */
      }

      /* Adjust column proportions for mobile */
      .product-column {
        flex: 1.5;
      }

      .qty-column {
        flex: 0.2;
        font-size: 10px;
      }

      .price-column {
        flex: 0.4;
        font-size: 10px;
      }
    }
    .hidden {
      display: none;
    }
    .remove-button {
      background-color: #ff4d4d;
      color: white;
      border: none;
      padding: 5px 10px;
      border-radius: 4px;
      cursor: pointer;
    }
    .remove-button:hover {
      background-color: #e60000;
    }


    /* Loading Screen Styles */
    #loadingScreen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.9);
      z-index: 9999;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }
    #loadingScreen .spinner {
      border: 8px solid #f3f3f3; /* Light grey */
      border-top: 8px solid #3498db; /* Blue */
      border-radius: 50%;
      width: 60px;
      height: 60px;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }
    #loadingScreen p {
      font-size: 18px;
      color: #333;
    }
    /* Toast Container and Toast Styles */
    .toast-container {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 10000; /* on top of loading screen */
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
    .toast {
      background-color: #333;
      color: #fff;
      padding: 10px 15px;
      border-radius: 5px;
      opacity: 0.95;
      font-size: 14px;
      animation: fadeInOut 3s forwards;
    }
    .toast.success {
      background-color: #28a745;
    }
    .toast.error {
      background-color: #dc3545;
    }
    .toast.info {
      background-color: #007bff;
    }
    @keyframes fadeInOut {
      0% { opacity: 0; }
      10% { opacity: 0.95; }
      90% { opacity: 0.95; }
      100% { opacity: 0; }
    }

    /* Admin navigation button styles */
    .admin-nav-button {
      background-color: #9b59b6;
      color: white;
      padding: 10px 20px;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      text-decoration: none;
      display: inline-block;
      transition: background-color 0.3s ease;
      margin-right: 10px;
    }

    .admin-nav-button:hover {
      background-color: #8e44ad;
      color: white;
    }

    .admin-nav-button.hidden {
      display: none;
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div id="loadingScreen">
    <div class="spinner"></div>
    <p>Loading products, please wait...</p>
  </div>

  <!-- Toast Container (for all messages) -->
  <div class="toast-container" id="toastContainer"></div>

  <div class="container">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
      <h1 style="margin: 0;">Receipt and Quotation Generator</h1>
      <div style="display: flex; align-items: center;">
        <a href="Admin/index.html" id="adminDashboardBtn" class="admin-nav-button hidden">Admin Dashboard</a>
        <button onclick="logout()" style="background-color: #e74c3c; padding: 8px 16px; font-size: 14px;">Logout</button>
      </div>
    </div>

    <!-- Company Selection Section -->
    <div class="section">
      <h2>Select Company</h2>
      <label for="companySelect">Choose a company:</label>
      <select id="companySelect" required>
        <option value="" disabled selected>Select a company</option>
        <option value="company1">Shans Accessories PTY LTD</option>
        <option value="company2">Shans Autosport PTY LTD</option>
      </select>
    </div>

    <!-- Customer Information Section -->
    <div class="section">
      <h2>Customer Information</h2>
      <label for="customerName">Name:</label>
      <input type="text" id="customerName">

      <label for="customerEmail">Email:</label>
      <input type="email" id="customerEmail">

      <label for="customerAddress">Address:</label>
      <input type="text" id="customerAddress">

      <label for="customerPhone">Phone:</label>
      <input type="tel" id="customerPhone">

      <label for="salespersonName">Salesperson Name:</label>
      <input type="text" id="salespersonName" placeholder="Enter your name...">
    </div>

    <!-- Shipping Information Section -->
    <div class="section">
      <h2>Shipping Information</h2>
      <div style="display: flex; gap: 10px; align-items: center;">
        <label for="sameAsBilling">Ship to the same address as billing</label>
        <input type="checkbox" id="sameAsBilling" checked>
      </div>

      <div id="shippingInfo" class="hidden">
        <label for="shippingName">Name:</label>
        <input type="text" id="shippingName">

        <label for="shippingEmail">Email:</label>
        <input type="email" id="shippingEmail">

        <label for="shippingAddress">Address:</label>
        <input type="text" id="shippingAddress">

        <label for="shippingPhone">Phone:</label>
        <input type="tel" id="shippingPhone">
      </div>
    </div>

    <!-- Add Products Section -->
    <div class="section">
      <h2>Add Products</h2>
      <label for="productSearch">Search Products:</label>
      <input type="text" id="productSearch" placeholder="Type to search products or enter new product">
      <div style="display: flex; align-items: center; margin-bottom: 10px;">
        <ul id="productList"></ul>
        <div class="loader" id="searchLoader"></div>
      </div>

      <label for="quantity">Quantity:</label>
      <div>
        <input type="number" id="quantity" value="1" min="1">
      </div>

      <label for="price">Price (R):</label>
      <input type="number" id="price" value="0" min="0" step="1">

      <label for="room" class="hidden" id="roomLabel">Room:</label>
      <input type="text" id="room" class="hidden" placeholder="Select a product to see the room" readonly>

      <button onclick="addSelectedProduct()">Add Product</button>
    </div>

    <!-- Selected Products and Totals Section -->
    <div class="section">
      <h2>Selected Products</h2>
      <!-- Div-based Table (works on all screen sizes) -->
      <div class="table-container">
        <div class="div-table">
          <!-- Table Header - Will be populated by JavaScript -->
          <div class="div-table-row div-table-header" id="table-header">
            <!-- Header cells will be added dynamically -->
          </div>
          <!-- Table Body - Will be populated by JavaScript -->
          <div class="div-table-body" id="selectedProductsBody">
            <!-- Product rows will be added dynamically -->
          </div>
        </div>
      </div>

      <div style="display: flex; align-items: center;">
        <label for="Tax">Add Tax</label>
        <input type="checkbox" id="Tax" checked>
      </div>

      <!-- Display the calculated values -->
      <p>Product Amount (Subtotal): R<span id="subtotalAmount">0.00</span></p>
      <p>Tax (15%): R<span id="taxAmount">0.00</span></p>
      <p>Total Amount: R<span id="totalAmount">0.00</span></p>

      <!-- Comments Section -->
      <div class="section" style="border:none; padding:0;">
        <label for="paymentMethod">Payment Method:</label>
        <select id="paymentMethod">
          <option value="Cash">Cash</option>
          <option value="EFT">EFT</option>
        </select>
      </div>

      <!-- Added Comments Box -->
      <div class="section" style="border:none; padding:0;">
        <label for="comments">Additional Comments:</label>
        <textarea id="comments" placeholder="Enter any additional comments here..."></textarea>
      </div>

      <div class="button-group">
        <button onclick="generateReceipt()">Generate Receipt</button>
        <button onclick="generateQuotation()">Generate Quotation</button>
        <button onclick="generateInvoice()">Generate Invoice</button>
      </div>
    </div>
  </div>

  <script>
    let selectedProducts = [];
    let allProducts = []; // To store all fetched products
    let newProductCounter = 1; // Counter for new products

    const API_BASE_URL = 'https://shans-backend-1.onrender.com/api';
    const CURRENCY_SYMBOL = 'R';
    const TAX_RATE = 0.15;

    const productSearchInput = document.getElementById('productSearch');
    const productList = document.getElementById('productList');
    const searchLoader = document.getElementById('searchLoader');
    const roomInput = document.getElementById('room');
    const roomLabel = document.getElementById('roomLabel');
    const sameAsBillingCheckbox = document.getElementById('sameAsBilling');
    const shippingInfoDiv = document.getElementById('shippingInfo');
    const paymentMethodSelect = document.getElementById('paymentMethod');
    const taxCheckbox = document.getElementById('Tax');
    const taxAmountSpan = document.getElementById('taxAmount');
    const subtotalAmountSpan = document.getElementById('subtotalAmount');
    const totalAmountSpan = document.getElementById('totalAmount');
    const tableHeader = document.getElementById('table-header');
    const selectedProductsBody = document.getElementById('selectedProductsBody');
    const companySelect = document.getElementById('companySelect');
    const commentsTextarea = document.getElementById('comments');
    const loadingScreen = document.getElementById('loadingScreen');

    // Company Information
    const companies = {
      company1: {
        name: "Shans Accessories PTY LTD",
        bankingInformation: `
          First National Bank<br>
          Account :  ***********<br>
          Branch code 257705<br>
          Swift code FIRNZAJJ
        `
      },
      company2: {
        name: "Shans Autosport PTY LTD",
        bankingInformation: `
          Business Account<br>
          Capitec Current Account<br>
          Account: **********
        `
      }
    };

    /**
     * Display a non-blocking toast message in the top-right corner.
     * @param {string} message - The message to display
     * @param {string} [type='info'] - 'success', 'error', or 'info'
     */
    function showToastMessage(message, type = 'info') {
      const toastContainer = document.getElementById('toastContainer');
      const toast = document.createElement('div');
      toast.classList.add('toast', type);
      toast.textContent = message;
      toastContainer.appendChild(toast);
      setTimeout(() => {
        toast.remove();
      }, 3000);
    }

    function setCookie(name, value, days) {
      const d = new Date();
      d.setTime(d.getTime() + (days*24*60*60*1000));
      const expires = "expires="+ d.toUTCString();
      document.cookie = name + "=" + encodeURIComponent(value) + ";" + expires + ";path=/";
    }

    function getCookie(name) {
      const cname = name + "=";
      const decodedCookie = decodeURIComponent(document.cookie);
      const ca = decodedCookie.split(';');
      for(let i = 0; i < ca.length; i++) {
        let c = ca[i].trim();
        if (c.indexOf(cname) === 0) {
          return c.substring(cname.length, c.length);
        }
      }
      return "";
    }

    async function fetchAllProducts() {
      try {
        const response = await fetch(`${API_BASE_URL}/products`);
        if (!response.ok) throw new Error('Network response was not ok');
        allProducts = await response.json();
      } catch (error) {
        console.error('Error fetching products:', error);
        showToastMessage('Failed to load products. Please try again later.', 'error');
      }
    }

    function displayFilteredProducts() {
      const searchTerm = productSearchInput.value.toLowerCase();
      const filteredProducts = allProducts.filter(product =>
        product.item_name.toLowerCase().includes(searchTerm)
      );

      productList.innerHTML = '';

      if (filteredProducts.length === 0 && searchTerm !== '') {
        const li = document.createElement('li');
        li.textContent = 'No products found';
        li.style.cursor = 'default';
        productList.appendChild(li);
        return;
      }

      // Group products by name to highlight duplicates in different rooms
      const productsByName = {};
      filteredProducts.forEach(product => {
        if (!productsByName[product.item_name]) {
          productsByName[product.item_name] = [];
        }
        productsByName[product.item_name].push(product);
      });

      // Display products, highlighting those with the same name in different rooms
      filteredProducts.forEach(product => {
        const li = document.createElement('li');
        const hasDuplicates = productsByName[product.item_name].length > 1;

        // Create product name element
        const productNameDiv = document.createElement('div');
        productNameDiv.className = 'product-name';

        // If there are duplicates of this product name in different rooms, highlight it
        if (hasDuplicates) {
          productNameDiv.innerHTML = `${product.item_name} <span style="color: #0066cc; font-weight: bold;">(${product.room_name})</span>`;
        } else {
          productNameDiv.textContent = product.item_name;
        }

        // Create product details element
        const productDetailsDiv = document.createElement('div');
        productDetailsDiv.className = 'product-details';

        // Show room and stock information
        const stockClass = product.available_stock > 0 ? 'stock-available' : 'stock-empty';
        productDetailsDiv.innerHTML = `Room: <b>${product.room_name || 'N/A'}</b> | Stock: <span class="${stockClass}">${product.available_stock}</span>`;

        // Add elements to list item
        li.appendChild(productNameDiv);
        li.appendChild(productDetailsDiv);
        li.onclick = () => selectProduct(product);

        // Add a subtle background color for items with duplicates to make them more noticeable
        if (hasDuplicates) {
          li.style.backgroundColor = '#f0f7ff';
        }

        productList.appendChild(li);
      });
    }

    function selectProduct(product) {
      productSearchInput.value = product.item_name;
      productSearchInput.dataset.itemCode = product.item_code; // Store the item_code in the dataset
      productSearchInput.dataset.roomId = product.room_id; // Store the room_id in the dataset
      productList.innerHTML = '';
      // Set the default price from the database, but this can be modified by the user
      // for negotiated prices or discounts before adding to the order
      document.getElementById('price').value = Math.round(parseFloat(product.unit_retail_price));

      // Get the quantity input element
      const quantityInput = document.getElementById('quantity');

      // Set the minimum attribute to 1
      quantityInput.setAttribute('min', 1);

      // Set the default quantity to 1
      quantityInput.value = 1;

      roomInput.value = product.room_name;
      roomInput.classList.remove('hidden');
      roomLabel.classList.remove('hidden');
    }

    function addSelectedProduct() {
      const productName = productSearchInput.value.trim();
      const quantity = parseInt(document.getElementById('quantity').value);
      // Get the price from the input field - this may be the original retail price
      // or a negotiated/discounted price entered by the user
      const price = Math.round(parseFloat(document.getElementById('price').value));

      // Get the item_code and room_id from the dataset if available
      const itemCode = productSearchInput.dataset.itemCode;
      const roomId = productSearchInput.dataset.roomId;

      // Find the product by item_code if available, otherwise fall back to finding by name
      let product;
      if (itemCode) {
        // Find the exact product by item_code (which is unique)
        product = allProducts.find(p => p.item_code === itemCode);
      } else {
        // Fall back to finding by name (for backward compatibility or manually entered products)
        product = allProducts.find(p => p.item_name.toLowerCase() === productName.toLowerCase());
      }

      if (productName === '' || isNaN(quantity) || isNaN(price)) {
        showToastMessage('Please enter valid product details.', 'error');
        return;
      }
      if (quantity <= 0) {
        showToastMessage('Quantity must be at least 1.', 'error');
        return;
      }
      if (price < 0) {
        showToastMessage('Price cannot be negative.', 'error');
        return;
      }
      if (product && quantity > product.available_stock) {
        showToastMessage(`Cannot add ${quantity} units. Only ${product.available_stock} available in stock.`, 'error');
        return;
      }

      // We'll store the typed-in price in product.price
      // Then let the table logic figure out net vs. tax if needed
      let taxPerProduct = 0;
      if (taxCheckbox.checked) {
        taxPerProduct = Math.round(price * TAX_RATE);
      }

      if (product) {
        selectedProducts.push({
          item_code: product.item_code,
          name: productName,
          room_name: product.room_name, // Store room name for reference
          quantity: quantity,
          price: parseFloat(price),
          tax_per_product: taxPerProduct,
          is_new: false
        });
        product.available_stock -= quantity; // update stock
      } else {
        const item_code = `NEW-${newProductCounter++}`;
        selectedProducts.push({
          item_code: item_code,
          name: productName,
          room_name: 'N/A', // No room for new products
          quantity: quantity,
          price: parseFloat(price),
          tax_per_product: taxPerProduct,
          is_new: true
        });
      }

      updateSelectedProductsTable();
      productSearchInput.value = '';

      // Clear the stored item_code and room_id
      delete productSearchInput.dataset.itemCode;
      delete productSearchInput.dataset.roomId;

      // Reset quantity input field
      const quantityInput = document.getElementById('quantity');
      quantityInput.value = '1';
      quantityInput.setAttribute('min', '1');

      // Reset price and room fields
      document.getElementById('price').value = '0';
      roomInput.value = '';
      roomInput.classList.add('hidden');
      roomLabel.classList.add('hidden');
    }

    /**
     * If tax is checked:
     *   - "Unit Price (R)" = price * 0.85
     *   - "Tax per Unit (R)" = price * 0.15
     *   - "Total (R)" = price * quantity
     *
     * If tax is NOT checked:
     *   - "Unit Price (R)" = price (as typed)
     *   - No separate tax column
     *   - "Total (R)" = price * quantity
     */
    function updateSelectedProductsTable() {
      // Clear the table body
      const tableBody = document.getElementById('selectedProductsBody');
      tableBody.innerHTML = '';

      // Update the table header
      updateTableHeader();

      // Show a message if no products are selected
      if (selectedProducts.length === 0) {
        const emptyRow = document.createElement('div');
        emptyRow.className = 'div-table-row';
        emptyRow.style.justifyContent = 'center';
        emptyRow.style.padding = '20px';
        emptyRow.style.color = '#666';
        emptyRow.innerHTML = '<i>No products selected yet. Search and add products above.</i>';
        tableBody.appendChild(emptyRow);
        calculateAndDisplayTotal();
        return;
      }

      // Add product rows
      selectedProducts.forEach((product, index) => {
        // Create a new row
        const row = document.createElement('div');
        row.className = 'div-table-row';

        // Calculate prices
        const netPrice = taxCheckbox.checked ? product.price * (1 - TAX_RATE) : product.price;
        const taxAmount = taxCheckbox.checked ? product.price * TAX_RATE : 0;
        const lineTotal = product.price * product.quantity;

        // Product name cell
        const nameCell = document.createElement('div');
        nameCell.className = 'div-table-cell product-column';

        // Show product name and room if available
        if (product.room_name && !product.is_new) {
          nameCell.innerHTML = `${product.name} <span style="color: #666; font-size: 0.85em;">(${product.room_name})</span>`;
        } else {
          nameCell.textContent = product.name;
        }

        if (product.is_new) {
          const newLabel = document.createElement('span');
          newLabel.className = 'new-product-label';
          newLabel.textContent = ' (New)';
          nameCell.appendChild(newLabel);
        }

        row.appendChild(nameCell);

        // Quantity cell
        const qtyCell = document.createElement('div');
        qtyCell.className = 'div-table-cell qty-column';
        qtyCell.textContent = product.quantity;
        qtyCell.style.fontWeight = '700'; // Make QTY bold
        qtyCell.style.textAlign = 'center';
        row.appendChild(qtyCell);

        if (taxCheckbox.checked) {
          // Unit Price cell
          const unitPriceCell = document.createElement('div');
          unitPriceCell.className = 'div-table-cell price-column';
          unitPriceCell.textContent = `${CURRENCY_SYMBOL}${netPrice.toFixed(0)}`;
          row.appendChild(unitPriceCell);

          // Tax cell
          const taxCell = document.createElement('div');
          taxCell.className = 'div-table-cell price-column tax-column';
          taxCell.textContent = `${CURRENCY_SYMBOL}${taxAmount.toFixed(0)}`;
          row.appendChild(taxCell);

          // Total cell
          const totalCell = document.createElement('div');
          totalCell.className = 'div-table-cell price-column';
          totalCell.textContent = `${CURRENCY_SYMBOL}${lineTotal.toFixed(0)}`;
          row.appendChild(totalCell);
        } else {
          // Unit Price cell
          const unitPriceCell = document.createElement('div');
          unitPriceCell.className = 'div-table-cell price-column';
          unitPriceCell.textContent = `${CURRENCY_SYMBOL}${product.price.toFixed(0)}`;
          row.appendChild(unitPriceCell);

          // Total cell
          const totalCell = document.createElement('div');
          totalCell.className = 'div-table-cell price-column';
          totalCell.textContent = `${CURRENCY_SYMBOL}${lineTotal.toFixed(0)}`;
          row.appendChild(totalCell);
        }

        // Action cell with red X instead of remove button
        const actionCell = document.createElement('div');
        actionCell.className = 'div-table-cell';
        actionCell.style.justifyContent = 'center';
        actionCell.style.width = '40px'; // Fixed width for X column
        actionCell.style.minWidth = '40px';

        const removeX = document.createElement('span');
        removeX.innerHTML = '&#10006;'; // X symbol
        removeX.className = 'remove-x';
        removeX.title = 'Remove item';
        removeX.onclick = () => removeProduct(index);

        actionCell.appendChild(removeX);
        row.appendChild(actionCell);

        // Add the row to the table body
        tableBody.appendChild(row);
      });

      calculateAndDisplayTotal();
    }

    /**
     * If tax is checked:
     *   - Subtotal = sum of (price * 0.85 * quantity)
     *   - Tax = sum of (price * 0.15 * quantity)
     *   - Total = sum of (price * quantity)
     *
     * If tax is NOT checked:
     *   - Subtotal = sum of (price * quantity)
     *   - Tax = 0
     *   - Total = sum of (price * quantity)
     */
    function calculateAndDisplayTotal() {
      let subtotal = 0;
      let tax = 0;
      let total = 0;

      selectedProducts.forEach(product => {
        if (taxCheckbox.checked) {
          subtotal += (product.price * (1 - TAX_RATE)) * product.quantity;
          tax += (product.price * TAX_RATE) * product.quantity;
          total += (product.price * product.quantity);
        } else {
          subtotal += (product.price * product.quantity);
          total += (product.price * product.quantity);
        }
      });

      if (!taxCheckbox.checked) {
        tax = 0;
      }

      subtotalAmountSpan.textContent = subtotal.toFixed(0);
      taxAmountSpan.textContent = tax.toFixed(0);
      totalAmountSpan.textContent = total.toFixed(0);
    }

    function updateTableHeader() {
      // Clear the header
      const header = document.getElementById('table-header');
      header.innerHTML = '';

      // Product column
      const productHeader = document.createElement('div');
      productHeader.className = 'div-table-cell div-table-heading product-column';
      productHeader.textContent = 'Product';
      header.appendChild(productHeader);

      // QTY column
      const qtyHeader = document.createElement('div');
      qtyHeader.className = 'div-table-cell div-table-heading qty-column';
      qtyHeader.textContent = 'QTY';
      qtyHeader.style.fontWeight = '700';
      qtyHeader.style.textAlign = 'center';
      qtyHeader.style.fontSize = '13px';
      header.appendChild(qtyHeader);

      // Price column (simplified name)
      const priceHeader = document.createElement('div');
      priceHeader.className = 'div-table-cell div-table-heading price-column';
      priceHeader.textContent = 'PRICE';
      priceHeader.style.fontSize = '13px';
      header.appendChild(priceHeader);

      if (taxCheckbox.checked) {
        // Tax column (only if tax is checked, simplified name)
        const taxHeader = document.createElement('div');
        taxHeader.className = 'div-table-cell div-table-heading price-column tax-column';
        taxHeader.textContent = 'TAX';
        taxHeader.style.fontSize = '13px';
        header.appendChild(taxHeader);
      }

      // Total column
      const totalHeader = document.createElement('div');
      totalHeader.className = 'div-table-cell div-table-heading price-column';
      totalHeader.textContent = 'TOTAL';
      totalHeader.style.fontSize = '13px';
      header.appendChild(totalHeader);

      // Action column (X)
      const actionHeader = document.createElement('div');
      actionHeader.className = 'div-table-cell div-table-heading';
      actionHeader.innerHTML = '&nbsp;'; // Empty header for the X column
      actionHeader.style.textAlign = 'center';
      actionHeader.style.width = '40px'; // Fixed width for X column
      header.appendChild(actionHeader);
    }

    function removeProduct(index) {
      const removedProduct = selectedProducts.splice(index, 1)[0];
      updateSelectedProductsTable();

      // Restore stock if it was an existing product
      if (!removedProduct.is_new && removedProduct && allProducts.length > 0) {
        const product = allProducts.find(p => p.item_code === removedProduct.item_code);
        if (product) {
          product.available_stock += removedProduct.quantity;
        }
      }

      showToastMessage(`Removed ${removedProduct.name} from the selection.`, 'info');
    }

    function generateReceipt() {
      console.log('Starting generateReceipt function');
      const selectedCompanyCookie = getCookie('selectedCompany');
      if (!selectedCompanyCookie) {
        showToastMessage('Please select a company before generating a receipt.', 'error');
        return;
      }
      // Always ensure email has the default value if empty
      const customerEmail = document.getElementById('customerEmail').value.trim();
      const billingInfo = {
        name: document.getElementById('customerName').value.trim(),
        email: customerEmail || '<EMAIL>',
        address: document.getElementById('customerAddress').value.trim(),
        phone: document.getElementById('customerPhone').value.trim()
      };
      console.log('Billing info:', billingInfo);

      let shippingInfo = null;
      if (!sameAsBillingCheckbox.checked) {
        // Always ensure shipping email has the default value if empty
        const shippingEmail = document.getElementById('shippingEmail').value.trim();
        shippingInfo = {
          name: document.getElementById('shippingName').value.trim(),
          email: shippingEmail || '<EMAIL>',
          address: document.getElementById('shippingAddress').value.trim(),
          phone: document.getElementById('shippingPhone').value.trim()
        };
      }

      if (selectedProducts.length === 0) {
        showToastMessage('No products selected.', 'error');
        return;
      }
      console.log('Products validation passed');

      const paymentMethod = paymentMethodSelect.value;
      const comments = commentsTextarea.value.trim();
      const salespersonName = document.getElementById('salespersonName').value.trim();
      console.log('Salesperson name:', salespersonName);

      const customerInfo = {
        billing: billingInfo,
        shipping: sameAsBillingCheckbox.checked ? null : shippingInfo,
        paymentMethod: paymentMethod,
        comments: comments,
        salespersonName: salespersonName
      };

      const selectedCompany = JSON.parse(selectedCompanyCookie);
      setCookie('customerInfo', JSON.stringify(customerInfo), 1);
      setCookie('selectedProducts', JSON.stringify(selectedProducts), 1);
      setCookie('subtotalAmount', JSON.stringify(subtotalAmountSpan.textContent), 1);
      setCookie('taxAmount', JSON.stringify(taxAmountSpan.textContent), 1);
      setCookie('totalAmount', JSON.stringify(totalAmountSpan.textContent), 1);
      setCookie('selectedCompany', JSON.stringify(selectedCompany), 1);
      console.log('All cookies set, redirecting to receipt.html');

      window.location.href = 'receipt.html';
    }

    function generateQuotation() {
      console.log('Starting generateQuotation function');
      const selectedCompanyCookie = getCookie('selectedCompany');
      if (!selectedCompanyCookie) {
        showToastMessage('Please select a company before generating a quotation.', 'error');
        return;
      }

      // Always ensure email has the default value if empty
      const customerEmail = document.getElementById('customerEmail').value.trim();
      const billingInfo = {
        name: document.getElementById('customerName').value.trim(),
        email: customerEmail || '<EMAIL>',
        address: document.getElementById('customerAddress').value.trim(),
        phone: document.getElementById('customerPhone').value.trim()
      };

      let shippingInfo = null;
      if (!sameAsBillingCheckbox.checked) {
        // Always ensure shipping email has the default value if empty
        const shippingEmail = document.getElementById('shippingEmail').value.trim();
        shippingInfo = {
          name: document.getElementById('shippingName').value.trim(),
          email: shippingEmail || '<EMAIL>',
          address: document.getElementById('shippingAddress').value.trim(),
          phone: document.getElementById('shippingPhone').value.trim()
        };
      }

      if (selectedProducts.length === 0) {
        showToastMessage('No products selected.', 'error');
        return;
      }

      const comments = commentsTextarea.value.trim();
      const salespersonName = document.getElementById('salespersonName').value.trim();

      const customerInfo = {
        billing: billingInfo,
        shipping: sameAsBillingCheckbox.checked ? null : shippingInfo,
        paymentMethod: 'N/A',
        comments: comments,
        salespersonName: salespersonName,
        timestamp: Date.now().toString() // Add timestamp to track when this quotation was created
      };

      const selectedCompany = JSON.parse(selectedCompanyCookie);
      setCookie('customerInfo', JSON.stringify(customerInfo), 1);
      setCookie('selectedProducts', JSON.stringify(selectedProducts), 1);
      setCookie('subtotalAmount', JSON.stringify(subtotalAmountSpan.textContent), 1);
      setCookie('taxAmount', JSON.stringify(taxAmountSpan.textContent), 1);
      setCookie('totalAmount', JSON.stringify(totalAmountSpan.textContent), 1);
      setCookie('selectedCompany', JSON.stringify(selectedCompany), 1);
      console.log('All cookies set, redirecting to quotation.html');

      window.location.href = 'quotation.html';
    }

    function generateInvoice() {
      console.log('Starting generateInvoice function');
      const selectedCompanyCookie = getCookie('selectedCompany');
      if (!selectedCompanyCookie) {
        showToastMessage('Please select a company before generating an invoice.', 'error');
        return;
      }

      // Always ensure email has the default value if empty
      const customerEmail = document.getElementById('customerEmail').value.trim();
      const billingInfo = {
        name: document.getElementById('customerName').value.trim(),
        email: customerEmail || '<EMAIL>',
        address: document.getElementById('customerAddress').value.trim(),
        phone: document.getElementById('customerPhone').value.trim()
      };

      let shippingInfo = null;
      if (!sameAsBillingCheckbox.checked) {
        // Always ensure shipping email has the default value if empty
        const shippingEmail = document.getElementById('shippingEmail').value.trim();
        shippingInfo = {
          name: document.getElementById('shippingName').value.trim(),
          email: shippingEmail || '<EMAIL>',
          address: document.getElementById('shippingAddress').value.trim(),
          phone: document.getElementById('shippingPhone').value.trim()
        };
      }

      if (selectedProducts.length === 0) {
        showToastMessage('No products selected.', 'error');
        return;
      }

      const paymentMethod = paymentMethodSelect.value;
      const comments = commentsTextarea.value.trim();
      const salespersonName = document.getElementById('salespersonName').value.trim();

      const customerInfo = {
        billing: billingInfo,
        shipping: sameAsBillingCheckbox.checked ? null : shippingInfo,
        paymentMethod: paymentMethod,
        comments: comments,
        salespersonName: salespersonName
      };

      const selectedCompany = JSON.parse(selectedCompanyCookie);
      setCookie('customerInfo', JSON.stringify(customerInfo), 1);
      setCookie('selectedProducts', JSON.stringify(selectedProducts), 1);
      setCookie('subtotalAmount', JSON.stringify(subtotalAmountSpan.textContent), 1);
      setCookie('taxAmount', JSON.stringify(taxAmountSpan.textContent), 1);
      setCookie('totalAmount', JSON.stringify(totalAmountSpan.textContent), 1);
      setCookie('selectedCompany', JSON.stringify(selectedCompany), 1);
      console.log('All cookies set, redirecting to invoice.html');

      window.location.href = 'invoice.html';
    }

    function toggleShippingInfo() {
      if (sameAsBillingCheckbox.checked) {
        shippingInfoDiv.classList.add('hidden');
      } else {
        shippingInfoDiv.classList.remove('hidden');
      }
    }

    function setCompanyInfo() {
      const selectedCompanyKey = companySelect.value;
      if (!selectedCompanyKey || !companies[selectedCompanyKey]) {
        showToastMessage('Please select a valid company.', 'error');
        return;
      }
      const selectedCompany = companies[selectedCompanyKey];
      setCookie('selectedCompany', JSON.stringify({
        key: selectedCompanyKey,
        name: selectedCompany.name,
        bankingInformation: selectedCompany.bankingInformation
      }), 1);
    }

    companySelect.addEventListener('change', setCompanyInfo);

    productSearchInput.addEventListener('input', () => {
      searchLoader.style.display = 'inline-block';
      clearTimeout(window.searchTimeout);
      window.searchTimeout = setTimeout(() => {
        displayFilteredProducts();
        searchLoader.style.display = 'none';
      }, 300);
    });

    sameAsBillingCheckbox.addEventListener('change', toggleShippingInfo);

    // Recalculate net/tax for each product if the checkbox changes
    taxCheckbox.addEventListener('change', () => {
      updateSelectedProductsTable();
    });

    // Function to load existing order data when returning from document pages
    function loadExistingOrderData() {
      // Check if we're in edit mode (coming back from a document page)
      const isEditingOrder = sessionStorage.getItem('editingOrder') === 'true';

      if (isEditingOrder) {
        // Clear the editing flag
        sessionStorage.removeItem('editingOrder');

        // Load customer info
        const customerInfoCookie = getCookie('customerInfo');
        if (customerInfoCookie) {
          const customerInfo = JSON.parse(customerInfoCookie);

          // Fill in customer information
          document.getElementById('customerName').value = customerInfo.billing.name || '';
          document.getElementById('customerAddress').value = customerInfo.billing.address || '';
          document.getElementById('customerEmail').value = customerInfo.billing.email || '';
          document.getElementById('customerPhone').value = customerInfo.billing.phone || '';

          // Handle shipping info
          if (customerInfo.shipping) {
            sameAsBillingCheckbox.checked = false;
            shippingInfoDiv.classList.remove('hidden');
            document.getElementById('shippingName').value = customerInfo.shipping.name || '';
            document.getElementById('shippingAddress').value = customerInfo.shipping.address || '';
            document.getElementById('shippingEmail').value = customerInfo.shipping.email || '';
            document.getElementById('shippingPhone').value = customerInfo.shipping.phone || '';
          } else {
            sameAsBillingCheckbox.checked = true;
            shippingInfoDiv.classList.add('hidden');
          }

          // Set payment method
          if (customerInfo.paymentMethod) {
            document.getElementById('paymentMethod').value = customerInfo.paymentMethod;
          }

          // Set comments
          if (customerInfo.comments) {
            document.getElementById('comments').value = customerInfo.comments;
          }

          // Set salesperson name
          if (customerInfo.salespersonName) {
            document.getElementById('salespersonName').value = customerInfo.salespersonName;
          }
        }

        // Load selected products
        const selectedProductsCookie = getCookie('selectedProducts');
        if (selectedProductsCookie) {
          selectedProducts = JSON.parse(selectedProductsCookie);
          updateSelectedProductsTable();
        }

        // Show a message to the user
        showToastMessage('Order loaded for editing. You can modify products and details before generating a new document.', 'info');
      }
    }

    // Check authentication status
    async function checkAuthStatus() {
      const token = localStorage.getItem('authToken');
      if (!token) {
        // No token, redirect to login
        window.location.href = 'login.html';
        return false;
      }

      try {
        // Verify token with backend
        const response = await fetch(`${API_BASE_URL}/auth/me`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          // Token is invalid, redirect to login
          localStorage.removeItem('authToken');
          localStorage.removeItem('userInfo');
          window.location.href = 'login.html';
          return false;
        }

        const data = await response.json();

        // Allow admin users to access the user page if they navigate here intentionally
        // Only redirect on login, not on manual navigation

        return true;
      } catch (error) {
        console.error('Auth check failed:', error);
        localStorage.removeItem('authToken');
        localStorage.removeItem('userInfo');
        window.location.href = 'login.html';
        return false;
      }
    }

    // Check if user is admin and show admin button
    function checkAndShowAdminButton() {
      const userInfo = localStorage.getItem('userInfo');
      if (userInfo) {
        const user = JSON.parse(userInfo);
        const adminButton = document.getElementById('adminDashboardBtn');
        if (user.is_admin && adminButton) {
          adminButton.classList.remove('hidden');
        }
      }
    }

    // Add logout functionality
    function logout() {
      fetch(`${API_BASE_URL}/auth/logout`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      })
      .then(() => {
        localStorage.removeItem('authToken');
        localStorage.removeItem('userInfo');
        window.location.href = 'login.html';
      })
      .catch(error => {
        console.error('Logout error:', error);
        // Even if logout fails on server, clear local storage and redirect
        localStorage.removeItem('authToken');
        localStorage.removeItem('userInfo');
        window.location.href = 'login.html';
      });
    }

    window.onload = async function() {
      // Check authentication first
      const isAuthenticated = await checkAuthStatus();
      if (!isAuthenticated) {
        return;
      }

      // Check if user is admin and show admin button
      checkAndShowAdminButton();

      try {
        await fetchAllProducts();
      } finally {
        loadingScreen.style.display = 'none';
      }

      // Optionally load previously selected company
      const selectedCompanyCookie = getCookie('selectedCompany');
      if (selectedCompanyCookie) {
        const selectedCompany = JSON.parse(selectedCompanyCookie);
        companySelect.value = selectedCompany.key;
      }

      // Check if we're returning from a document page to edit an order
      loadExistingOrderData();

      // Update the products table (will show loaded products if in edit mode)
      updateSelectedProductsTable();
    };
  </script>

</body>
</html>
